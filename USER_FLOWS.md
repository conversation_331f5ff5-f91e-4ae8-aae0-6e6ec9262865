# User Flows - Aplicación de Atención Ciudadana AI-First

## Resumen de Flujos Críticos

Este documento define los user flows principales para cada módulo de la aplicación, basados en los epics y requisitos del PRD_chia.md.

### Flujos Incluidos
1. **Autenticación y Registro**
2. **Consulta con Chatbot AI**
3. **Búsqueda Semántica**
4. **Inicio de Trámite**
5. **Seguimiento de Trámite**
6. **Gestión Administrativa**
7. **Automatización de Procesos**

---

## 🔐 **FLUJO 1: AUTENTICACIÓN Y REGISTRO**

### Registro de Nuevo Ciudadano

```mermaid
flowchart TD
    A[Ciudadano accede al portal] --> B{¿Tiene cuenta?}
    B -->|No| C[Clic en 'Registrarse']
    B -->|Sí| D[Ir a Login]
    
    C --> E[Formulario de registro]
    E --> F[Ingresa datos básicos]
    F --> G[Acepta términos y condiciones]
    G --> H[Envía formulario]
    
    H --> I{¿Datos válidos?}
    I -->|No| J[Muestra errores] --> E
    I -->|Sí| K[Crea cuenta]
    
    K --> L[Envía email de verificación]
    L --> M[Muestra mensaje de confirmación]
    M --> N[Ciudadano revisa email]
    N --> O[Clic en enlace de verificación]
    O --> P[Cuenta activada]
    P --> Q[Redirige a dashboard]
    
    D --> R[Formulario de login]
    R --> S[Ingresa credenciales]
    S --> T{¿Credenciales válidas?}
    T -->|No| U[Muestra error] --> R
    T -->|Sí| V{¿2FA habilitado?}
    V -->|No| Q
    V -->|Sí| W[Solicita código 2FA]
    W --> X[Ingresa código]
    X --> Y{¿Código válido?}
    Y -->|No| Z[Error 2FA] --> W
    Y -->|Sí| Q
```

### Login de Administrador

```mermaid
flowchart TD
    A[Admin accede a /admin] --> B[Formulario de login admin]
    B --> C[Ingresa credenciales]
    C --> D{¿Credenciales válidas?}
    D -->|No| E[Error de autenticación] --> B
    D -->|Sí| F[Verifica roles y permisos]
    F --> G{¿Tiene permisos admin?}
    G -->|No| H[Acceso denegado]
    G -->|Sí| I[Carga dashboard admin]
    I --> J[Muestra módulos según permisos]
```

---

## 🤖 **FLUJO 2: CONSULTA CON CHATBOT AI**

### Interacción Básica con Chatbot

```mermaid
flowchart TD
    A[Ciudadano abre chatbot] --> B[Interfaz de chat se carga]
    B --> C[Muestra mensaje de bienvenida]
    C --> D[Ciudadano escribe pregunta]
    D --> E[Envía mensaje]
    
    E --> F[Sistema analiza intención]
    F --> G[Busca en base de conocimientos]
    G --> H{¿Encuentra respuesta?}
    
    H -->|Sí| I[Genera respuesta contextual]
    I --> J[Muestra respuesta al ciudadano]
    J --> K{¿Respuesta útil?}
    K -->|Sí| L[Ciudadano da feedback positivo]
    K -->|No| M[Ciudadano solicita más ayuda]
    
    H -->|No| N[Respuesta genérica]
    N --> O[Ofrece opciones de escalamiento]
    
    M --> P{¿Necesita agente humano?}
    P -->|Sí| Q[Inicia escalamiento]
    P -->|No| R[Reformula pregunta] --> F
    
    Q --> S[Transfiere a cola de agentes]
    S --> T[Agente humano toma caso]
    T --> U[Continúa conversación]
    
    L --> V[Registra interacción exitosa]
    V --> W[Actualiza métricas de IA]
```

### Escalamiento a Agente Humano

```mermaid
flowchart TD
    A[Chatbot no puede resolver] --> B[Detecta necesidad de escalamiento]
    B --> C[Muestra opciones al ciudadano]
    C --> D{¿Ciudadano acepta?}
    D -->|No| E[Continúa con chatbot]
    D -->|Sí| F[Recopila información adicional]
    
    F --> G[Crea ticket de soporte]
    G --> H[Asigna a cola apropiada]
    H --> I{¿Agente disponible?}
    
    I -->|Sí| J[Conecta inmediatamente]
    I -->|No| K[Muestra tiempo de espera]
    K --> L[Ofrece callback]
    L --> M{¿Acepta callback?}
    M -->|Sí| N[Programa llamada]
    M -->|No| O[Mantiene en cola]
    
    J --> P[Agente recibe contexto]
    P --> Q[Inicia conversación]
    Q --> R[Resuelve consulta]
    R --> S[Cierra ticket]
    S --> T[Solicita feedback]
```

---

## 🔍 **FLUJO 3: BÚSQUEDA SEMÁNTICA**

### Búsqueda de Información

```mermaid
flowchart TD
    A[Ciudadano accede a búsqueda] --> B[Barra de búsqueda visible]
    B --> C[Escribe consulta en lenguaje natural]
    C --> D[Sistema procesa consulta]
    
    D --> E[Genera embedding de consulta]
    E --> F[Busca en índice vectorial]
    F --> G[Aplica filtros de relevancia]
    G --> H[Rankea resultados]
    
    H --> I{¿Encuentra resultados?}
    I -->|No| J[Muestra mensaje sin resultados]
    J --> K[Sugiere términos alternativos]
    K --> L[Ofrece contacto con soporte]
    
    I -->|Sí| M[Muestra resultados rankeados]
    M --> N[Ciudadano revisa resultados]
    N --> O{¿Encuentra lo que busca?}
    
    O -->|Sí| P[Clic en resultado relevante]
    P --> Q[Navega a contenido]
    Q --> R[Registra búsqueda exitosa]
    
    O -->|No| S[Refina búsqueda]
    S --> T[Aplica filtros adicionales]
    T --> U[Nueva búsqueda] --> D
    
    O -->|Parcialmente| V[Usa filtros laterales]
    V --> W[Filtra por categoría/dependencia]
    W --> X[Actualiza resultados] --> M
```

### Búsqueda Avanzada con Filtros

```mermaid
flowchart TD
    A[Ciudadano en resultados básicos] --> B[Accede a filtros avanzados]
    B --> C[Selecciona filtros]
    C --> D{¿Qué tipo de filtro?}
    
    D -->|Dependencia| E[Filtra por organismo]
    D -->|Categoría| F[Filtra por tipo de servicio]
    D -->|Fecha| G[Filtra por actualización]
    D -->|Ubicación| H[Filtra por región]
    
    E --> I[Aplica filtro de dependencia]
    F --> I
    G --> I
    H --> I
    
    I --> J[Actualiza resultados]
    J --> K{¿Resultados satisfactorios?}
    K -->|Sí| L[Ciudadano encuentra información]
    K -->|No| M[Ajusta filtros] --> C
    
    L --> N[Accede a información detallada]
    N --> O[Registra búsqueda exitosa]
```

---

## 📋 **FLUJO 4: INICIO DE TRÁMITE**

### Solicitud de Nuevo Trámite

```mermaid
flowchart TD
    A[Ciudadano identifica trámite necesario] --> B{¿Está autenticado?}
    B -->|No| C[Redirige a login] --> D[Autentica] --> E
    B -->|Sí| E[Accede a formulario de trámite]
    
    E --> F[Muestra información del trámite]
    F --> G[Lista requisitos y documentos]
    G --> H[Ciudadano revisa información]
    H --> I{¿Cumple requisitos?}
    
    I -->|No| J[Muestra qué falta]
    J --> K[Ofrece ayuda para obtener documentos]
    K --> L[Ciudadano obtiene documentos] --> I
    
    I -->|Sí| M[Inicia formulario]
    M --> N[Completa datos personales]
    N --> O[Sube documentos requeridos]
    
    O --> P[Sistema valida documentos]
    P --> Q{¿Documentos válidos?}
    Q -->|No| R[Muestra errores específicos] --> O
    Q -->|Sí| S[Revisa información completa]
    
    S --> T[Ciudadano confirma envío]
    T --> U[Sistema crea trámite]
    U --> V[Asigna número de seguimiento]
    V --> W[Envía confirmación por email]
    W --> X[Muestra estado inicial]
    X --> Y[Inicia workflow automático]
```

### Validación Automática con IA

```mermaid
flowchart TD
    A[Documento subido] --> B[Sistema extrae datos con OCR]
    B --> C[IA analiza contenido]
    C --> D{¿Formato correcto?}
    
    D -->|No| E[Rechaza documento]
    E --> F[Especifica errores]
    F --> G[Solicita corrección]
    
    D -->|Sí| H[Valida datos extraídos]
    H --> I{¿Datos consistentes?}
    I -->|No| J[Marca para revisión manual]
    I -->|Sí| K[Aprueba automáticamente]
    
    K --> L[Continúa workflow]
    J --> M[Asigna a revisor humano]
    M --> N[Revisor valida manualmente]
    N --> O{¿Aprueba revisor?}
    O -->|Sí| L
    O -->|No| E
```

---

## 📊 **FLUJO 5: SEGUIMIENTO DE TRÁMITE**

### Consulta de Estado

```mermaid
flowchart TD
    A[Ciudadano quiere consultar estado] --> B{¿Cómo accede?}
    B -->|Portal web| C[Login en portal]
    B -->|Número de trámite| D[Ingresa número]
    B -->|Notificación| E[Clic en notificación]
    
    C --> F[Accede a 'Mis Trámites']
    D --> G[Búsqueda por número]
    E --> H[Acceso directo]
    
    F --> I[Lista todos sus trámites]
    G --> J{¿Número válido?}
    J -->|No| K[Error: trámite no encontrado]
    J -->|Sí| H
    
    I --> L[Selecciona trámite específico]
    L --> H
    H --> M[Muestra estado actual]
    
    M --> N[Timeline visual del progreso]
    N --> O[Detalles de etapa actual]
    O --> P[Documentos generados]
    P --> Q[Próximos pasos]
    
    Q --> R{¿Requiere acción ciudadano?}
    R -->|Sí| S[Muestra acciones pendientes]
    R -->|No| T[Muestra tiempo estimado]
    
    S --> U[Ciudadano realiza acción]
    U --> V[Actualiza estado]
    V --> W[Envía notificación]
```

### Notificaciones Proactivas

```mermaid
flowchart TD
    A[Sistema detecta cambio de estado] --> B[Identifica ciudadano]
    B --> C[Verifica preferencias de notificación]
    C --> D{¿Qué canal prefiere?}
    
    D -->|Email| E[Envía email]
    D -->|SMS| F[Envía SMS]
    D -->|Push| G[Envía notificación push]
    D -->|Todos| H[Envía por todos los canales]
    
    E --> I[Registra envío de email]
    F --> J[Registra envío de SMS]
    G --> K[Registra notificación push]
    H --> L[Registra envío múltiple]
    
    I --> M[Ciudadano recibe notificación]
    J --> M
    K --> M
    L --> M
    
    M --> N{¿Ciudadano interactúa?}
    N -->|Sí| O[Accede a detalles del trámite]
    N -->|No| P[Notificación queda pendiente]
    
    O --> Q[Ve estado actualizado]
    P --> R[Sistema programa recordatorio]
```

---

## ⚙️ **FLUJO 6: GESTIÓN ADMINISTRATIVA**

### Creación de Nuevo Trámite (Admin)

```mermaid
flowchart TD
    A[Admin accede a gestión de contenido] --> B[Selecciona 'Crear Trámite']
    B --> C[Formulario de creación]
    C --> D[Completa información básica]
    D --> E[Define etapas del proceso]
    E --> F[Configura documentos requeridos]
    F --> G[Establece plazos y costos]
    G --> H[Asigna responsables]
    H --> I[Configura automatizaciones]
    I --> J[Preview del trámite]
    J --> K{¿Información correcta?}
    K -->|No| L[Edita información] --> D
    K -->|Sí| M[Envía para aprobación]
    M --> N{¿Requiere aprobación?}
    N -->|No| O[Publica inmediatamente]
    N -->|Sí| P[Asigna a supervisor]
    P --> Q[Supervisor revisa]
    Q --> R{¿Aprueba supervisor?}
    R -->|No| S[Devuelve con comentarios] --> L
    R -->|Sí| O
    O --> T[Trámite disponible para ciudadanos]
```

### Gestión de Base de Conocimientos

```mermaid
flowchart TD
    A[Admin accede a base de conocimientos] --> B[Ve lista de contenido]
    B --> C{¿Qué acción?}
    C -->|Crear| D[Nuevo artículo/FAQ]
    C -->|Editar| E[Selecciona contenido existente]
    C -->|Eliminar| F[Confirma eliminación]
    
    D --> G[Editor de contenido]
    E --> G
    G --> H[Escribe/edita contenido]
    H --> I[Categoriza información]
    I --> J[Asigna tags y metadatos]
    J --> K[Preview del contenido]
    K --> L{¿Contenido correcto?}
    L -->|No| M[Corrige contenido] --> H
    L -->|Sí| N[Publica contenido]
    N --> O[Actualiza índice de búsqueda]
    O --> P[Entrena modelo de IA]
    P --> Q[Contenido disponible en chatbot]
    
    F --> R[Confirma eliminación]
    R --> S[Remueve de índices]
    S --> T[Actualiza modelo de IA]
```

---

## 🔄 **FLUJO 7: AUTOMATIZACIÓN DE PROCESOS**

### Procesamiento Automático de Trámite

```mermaid
flowchart TD
    A[Trámite iniciado por ciudadano] --> B[Sistema identifica tipo de trámite]
    B --> C[Carga workflow configurado]
    C --> D[Inicia primera etapa]
    
    D --> E{¿Etapa automatizable?}
    E -->|No| F[Asigna a revisor humano]
    E -->|Sí| G[Ejecuta automatización]
    
    G --> H[IA valida documentos]
    H --> I{¿Validación exitosa?}
    I -->|No| J[Marca para revisión manual]
    I -->|Sí| K[Continúa automáticamente]
    
    K --> L[Ejecuta reglas de negocio]
    L --> M{¿Cumple criterios?}
    M -->|No| N[Rechaza automáticamente]
    M -->|Sí| O[Avanza a siguiente etapa]
    
    O --> P{¿Hay más etapas?}
    P -->|Sí| Q[Carga siguiente etapa] --> E
    P -->|No| R[Completa trámite]
    
    F --> S[Revisor humano evalúa]
    S --> T{¿Aprueba revisor?}
    T -->|Sí| O
    T -->|No| N
    
    J --> S
    N --> U[Notifica rechazo a ciudadano]
    R --> V[Notifica aprobación a ciudadano]
    V --> W[Genera documentos finales]
```

### Monitoreo y Optimización

```mermaid
flowchart TD
    A[Sistema monitorea procesos] --> B[Recolecta métricas]
    B --> C[Analiza rendimiento]
    C --> D{¿Detecta problemas?}
    
    D -->|No| E[Continúa monitoreo]
    D -->|Sí| F[Identifica cuellos de botella]
    
    F --> G{¿Tipo de problema?}
    G -->|Rendimiento| H[Optimiza consultas/procesos]
    G -->|Calidad| I[Ajusta modelos de IA]
    G -->|Proceso| J[Revisa workflow]
    
    H --> K[Implementa optimización]
    I --> L[Reentrena modelo]
    J --> M[Modifica proceso]
    
    K --> N[Valida mejora]
    L --> N
    M --> N
    
    N --> O{¿Mejora confirmada?}
    O -->|Sí| P[Despliega cambios]
    O -->|No| Q[Revierte cambios]
    
    P --> R[Actualiza documentación]
    Q --> S[Analiza causa raíz]
    S --> F
    
    E --> T[Genera reportes periódicos]
    R --> T
    T --> U[Envía a administradores]
```

---

## 📊 **Métricas de Éxito por Flujo**

| Flujo | Métrica Clave | Target | Punto de Medición |
|-------|---------------|--------|-------------------|
| Autenticación | Tasa de conversión registro | >80% | Verificación email |
| Chatbot | Resolución primera interacción | >85% | Feedback ciudadano |
| Búsqueda | Precisión resultados | >85% | Clics en top 3 |
| Inicio Trámite | Completitud formularios | >90% | Envío exitoso |
| Seguimiento | Satisfacción información | >90% | Encuesta post-consulta |
| Admin | Tiempo creación contenido | <15 min | Publicación exitosa |
| Automatización | Tasa de procesamiento automático | >60% | Sin intervención humana |

---

*Estos user flows proporcionan la base para el desarrollo de interfaces y la implementación de la lógica de negocio según los requisitos del PRD_chia.md.*
