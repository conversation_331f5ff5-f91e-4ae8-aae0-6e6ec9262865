# API Specification - Aplicación de Atención Ciudadana AI-First

## Resumen de APIs

Esta especificación define todas las APIs REST para la aplicación, organizadas por módulos funcionales según el PRD_chia.md.

### Arquitectura de APIs
- **Base URL**: `https://api.chia.gov.co/v1`
- **Autenticación**: JWT <PERSON> (Supabase Auth)
- **Formato**: JSON
- **Versionado**: URL path (`/v1/`, `/v2/`)
- **Rate Limiting**: 1000 requests/hour por usuario

### Códigos de Estado HTTP
- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error

---

## 🔐 **MÓDULO 1: AUTENTICACIÓN Y USUARIOS**

### 1.1 Registro de Usuario

```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "<PERSON>",
  "lastName": "<PERSON>",
  "documentType": "CC",
  "documentNumber": "12345678",
  "phone": "+57300123456",
  "acceptTerms": true
}
```

**Respuesta 201:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-user-id",
      "email": "<EMAIL>",
      "firstName": "Juan",
      "lastName": "Pérez",
      "emailVerified": false,
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "message": "Usuario registrado. Verifica tu email para activar la cuenta."
  }
}
```

### 1.2 Login de Usuario

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-user-id",
      "email": "<EMAIL>",
      "firstName": "Juan",
      "lastName": "Pérez",
      "role": "ciudadano"
    },
    "tokens": {
      "accessToken": "jwt-access-token",
      "refreshToken": "jwt-refresh-token",
      "expiresIn": 3600
    }
  }
}
```

### 1.3 Perfil de Usuario

```http
GET /auth/profile
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-user-id",
    "email": "<EMAIL>",
    "firstName": "Juan",
    "lastName": "Pérez",
    "documentType": "CC",
    "documentNumber": "12345678",
    "phone": "+57300123456",
    "preferences": {
      "notifications": {
        "email": true,
        "sms": false,
        "push": true
      },
      "language": "es"
    },
    "createdAt": "2024-01-15T10:30:00Z",
    "lastLogin": "2024-01-20T14:22:00Z"
  }
}
```

---

## 🤖 **MÓDULO 2: CHATBOT E IA**

### 2.1 Enviar Mensaje al Chatbot

```http
POST /chatbot/message
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "message": "¿Cómo puedo renovar mi cédula?",
  "sessionId": "uuid-session-id",
  "context": {
    "previousMessages": 3,
    "userLocation": "Bogotá",
    "userPreferences": ["tramites_identidad"]
  }
}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "response": "Para renovar tu cédula, necesitas...",
    "confidence": 0.95,
    "sources": [
      {
        "title": "Renovación de Cédula",
        "url": "/tramites/cedula-renovacion",
        "relevance": 0.98
      }
    ],
    "suggestedActions": [
      {
        "type": "start_process",
        "label": "Iniciar trámite de renovación",
        "url": "/tramites/cedula/iniciar"
      }
    ],
    "needsHumanAgent": false,
    "sessionId": "uuid-session-id",
    "messageId": "uuid-message-id"
  }
}
```

### 2.2 Escalamiento a Agente Humano

```http
POST /chatbot/escalate
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "sessionId": "uuid-session-id",
  "reason": "complex_query",
  "userMessage": "Mi caso es muy específico...",
  "priority": "normal"
}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "ticketId": "TICKET-2024-001",
    "estimatedWaitTime": 300,
    "queuePosition": 3,
    "agentAvailable": false,
    "callbackOffered": true,
    "message": "Te hemos conectado con un agente humano. Tiempo estimado: 5 minutos."
  }
}
```

### 2.3 Historial de Conversaciones

```http
GET /chatbot/history?limit=20&offset=0
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "sessionId": "uuid-session-id",
        "startedAt": "2024-01-20T10:00:00Z",
        "lastMessageAt": "2024-01-20T10:15:00Z",
        "messageCount": 8,
        "resolved": true,
        "satisfaction": 4.5,
        "topic": "renovacion_cedula"
      }
    ],
    "pagination": {
      "total": 45,
      "limit": 20,
      "offset": 0,
      "hasNext": true
    }
  }
}
```

---

## 🔍 **MÓDULO 3: BÚSQUEDA SEMÁNTICA**

### 3.1 Búsqueda General

```http
GET /search?q=renovar%20cedula&limit=10&offset=0
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "query": "renovar cedula",
    "results": [
      {
        "id": "doc-123",
        "title": "Renovación de Cédula de Ciudadanía",
        "snippet": "Proceso para renovar tu cédula cuando está próxima a vencer...",
        "url": "/tramites/cedula-renovacion",
        "type": "tramite",
        "category": "Identificación",
        "dependencia": "Registraduría Nacional",
        "relevanceScore": 0.95,
        "lastUpdated": "2024-01-10T00:00:00Z"
      }
    ],
    "totalResults": 25,
    "searchTime": 0.15,
    "suggestions": ["renovación cédula", "cédula vencida", "duplicado cédula"]
  }
}
```

### 3.2 Búsqueda con Filtros

```http
POST /search/advanced
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "query": "licencia de conducir",
  "filters": {
    "dependencia": ["Ministerio de Transporte"],
    "categoria": ["Transporte"],
    "dateRange": {
      "from": "2024-01-01",
      "to": "2024-12-31"
    },
    "type": ["tramite", "servicio"]
  },
  "limit": 10,
  "offset": 0
}
```

### 3.3 Sugerencias de Búsqueda

```http
GET /search/suggestions?q=lic&limit=5
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "suggestions": [
      "licencia de conducir",
      "licencia de construcción",
      "licencia comercial",
      "licencia ambiental",
      "licencia de funcionamiento"
    ]
  }
}
```

---

## 📋 **MÓDULO 4: TRÁMITES Y SERVICIOS**

### 4.1 Listar Trámites Disponibles

```http
GET /tramites?categoria=identificacion&dependencia=registraduria&limit=20
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "tramites": [
      {
        "id": "tramite-cedula-renovacion",
        "nombre": "Renovación de Cédula",
        "descripcion": "Renovación de cédula de ciudadanía próxima a vencer",
        "categoria": "Identificación",
        "dependencia": "Registraduría Nacional",
        "costo": 0,
        "tiempoEstimado": "5-10 días hábiles",
        "modalidad": ["presencial", "virtual"],
        "requisitos": [
          "Cédula actual",
          "Foto reciente",
          "Comprobante de pago"
        ],
        "documentosRequeridos": [
          {
            "nombre": "Cédula actual",
            "tipo": "imagen",
            "obligatorio": true
          }
        ],
        "disponible": true,
        "automatizable": true
      }
    ],
    "pagination": {
      "total": 150,
      "limit": 20,
      "offset": 0
    }
  }
}
```

### 4.2 Iniciar Nuevo Trámite

```http
POST /tramites/{tramiteId}/iniciar
Authorization: Bearer {jwt-token}
Content-Type: multipart/form-data

{
  "datosPersonales": {
    "nombres": "Juan Carlos",
    "apellidos": "Pérez García",
    "documentoTipo": "CC",
    "documentoNumero": "12345678"
  },
  "documentos": [
    {
      "tipo": "cedula_actual",
      "archivo": "base64-encoded-file"
    }
  ],
  "observaciones": "Solicitud urgente por viaje"
}
```

**Respuesta 201:**
```json
{
  "success": true,
  "data": {
    "tramiteId": "TR-2024-001234",
    "numeroSeguimiento": "2024001234",
    "estado": "iniciado",
    "fechaInicio": "2024-01-20T14:30:00Z",
    "tiempoEstimado": "5-10 días hábiles",
    "proximaEtapa": "validacion_documentos",
    "documentosRecibidos": 1,
    "documentosPendientes": 0,
    "urlSeguimiento": "/tramites/seguimiento/TR-2024-001234"
  }
}
```

### 4.3 Consultar Estado de Trámite

```http
GET /tramites/{tramiteId}/estado
Authorization: Bearer {jwt-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "tramiteId": "TR-2024-001234",
    "numeroSeguimiento": "2024001234",
    "estado": "en_proceso",
    "etapaActual": "revision_documentos",
    "progreso": 60,
    "fechaInicio": "2024-01-20T14:30:00Z",
    "fechaEstimadaFinalizacion": "2024-01-30T17:00:00Z",
    "timeline": [
      {
        "etapa": "iniciado",
        "fecha": "2024-01-20T14:30:00Z",
        "completada": true,
        "descripcion": "Trámite iniciado correctamente"
      },
      {
        "etapa": "validacion_documentos",
        "fecha": "2024-01-21T09:00:00Z",
        "completada": true,
        "descripcion": "Documentos validados automáticamente"
      },
      {
        "etapa": "revision_documentos",
        "fecha": "2024-01-22T10:00:00Z",
        "completada": false,
        "descripcion": "En revisión por funcionario"
      }
    ],
    "documentosGenerados": [
      {
        "nombre": "Comprobante de radicación",
        "url": "/documentos/TR-2024-001234/comprobante.pdf",
        "fechaGeneracion": "2024-01-20T14:30:00Z"
      }
    ],
    "accionesPendientes": [],
    "observaciones": "Trámite procesándose normalmente"
  }
}
```

### 4.4 Listar Mis Trámites

```http
GET /tramites/mis-tramites?estado=activo&limit=10&offset=0
Authorization: Bearer {jwt-token}
```

---

## 👥 **MÓDULO 5: ADMINISTRACIÓN**

### 5.1 Dashboard Administrativo

```http
GET /admin/dashboard
Authorization: Bearer {jwt-admin-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "estadisticas": {
      "tramitesHoy": 45,
      "tramitesPendientes": 123,
      "tramitesCompletados": 890,
      "tiempoPromedioResolucion": "3.2 días",
      "satisfaccionPromedio": 4.2
    },
    "alertas": [
      {
        "tipo": "warning",
        "mensaje": "5 trámites próximos a vencer SLA",
        "url": "/admin/tramites/vencimiento"
      }
    ],
    "actividadReciente": [
      {
        "tipo": "tramite_completado",
        "descripcion": "Renovación de cédula TR-2024-001230",
        "fecha": "2024-01-20T16:45:00Z"
      }
    ]
  }
}
```

### 5.2 Gestionar Contenido

```http
POST /admin/contenido
Authorization: Bearer {jwt-admin-token}
Content-Type: application/json

{
  "tipo": "faq",
  "titulo": "¿Cómo renovar mi cédula?",
  "contenido": "Para renovar tu cédula necesitas...",
  "categoria": "identificacion",
  "tags": ["cedula", "renovacion", "documentos"],
  "estado": "borrador",
  "dependencia": "registraduria"
}
```

### 5.3 Configurar Workflow

```http
POST /admin/workflows
Authorization: Bearer {jwt-admin-token}
Content-Type: application/json

{
  "tramiteId": "cedula-renovacion",
  "nombre": "Workflow Renovación Cédula",
  "etapas": [
    {
      "nombre": "validacion_inicial",
      "tipo": "automatica",
      "configuracion": {
        "validarDocumentos": true,
        "tiempoMaximo": 300
      }
    },
    {
      "nombre": "revision_humana",
      "tipo": "manual",
      "asignadoA": "funcionario_registraduria",
      "slaHoras": 24
    }
  ]
}
```

---

## 📊 **MÓDULO 6: MÉTRICAS Y REPORTES**

### 6.1 Métricas del Chatbot

```http
GET /metrics/chatbot?periodo=30d
Authorization: Bearer {jwt-admin-token}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "periodo": "30d",
    "totalInteracciones": 1250,
    "resolucionPrimeraInteraccion": 0.87,
    "tiempoPromedioRespuesta": 2.3,
    "satisfaccionPromedio": 4.1,
    "escalamientosHumanos": 163,
    "temasConsultados": [
      {
        "tema": "renovacion_cedula",
        "consultas": 245,
        "porcentaje": 19.6
      }
    ]
  }
}
```

### 6.2 Reportes de Trámites

```http
GET /metrics/tramites?desde=2024-01-01&hasta=2024-01-31
Authorization: Bearer {jwt-admin-token}
```

---

## 🔒 **SEGURIDAD Y AUTENTICACIÓN**

### Headers Requeridos
```http
Authorization: Bearer {jwt-token}
Content-Type: application/json
X-API-Version: v1
X-Request-ID: uuid-request-id
```

### Rate Limiting
- **Ciudadanos**: 1000 requests/hora
- **Administradores**: 5000 requests/hora
- **Sistemas**: 10000 requests/hora

### Códigos de Error Detallados
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Los datos enviados no son válidos",
    "details": [
      {
        "field": "email",
        "message": "El email no tiene un formato válido"
      }
    ],
    "requestId": "uuid-request-id"
  }
}
```

---

## 📝 **WEBHOOKS Y NOTIFICACIONES**

### 6.1 Configurar Webhook

```http
POST /webhooks
Authorization: Bearer {jwt-admin-token}
Content-Type: application/json

{
  "url": "https://mi-sistema.gov.co/webhook",
  "eventos": ["tramite.completado", "tramite.rechazado"],
  "secreto": "webhook-secret-key",
  "activo": true
}
```

### 6.2 Formato de Webhook

```json
{
  "evento": "tramite.completado",
  "timestamp": "2024-01-20T16:45:00Z",
  "data": {
    "tramiteId": "TR-2024-001234",
    "ciudadanoId": "uuid-user-id",
    "tipo": "renovacion_cedula",
    "resultado": "aprobado"
  },
  "signature": "sha256-hash"
}
```

---

*Esta especificación de APIs proporciona la base técnica completa para implementar todos los módulos definidos en el PRD_chia.md con enfoque en seguridad, escalabilidad y usabilidad.*
